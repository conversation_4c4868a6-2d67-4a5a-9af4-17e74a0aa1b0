import { SidebarInset, SidebarProvider, SidebarTrigger, useSidebar } from "../../../ui/sidebar";
import { ShadcnAppSidebar } from "./app-sidebar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from "~/components/ui/breadcrumb";
import { SideBarItem } from "~/application/sidebar/SidebarItem";
import { useLocation, useParams } from "react-router";
import { useTitleData } from "~/utils/data/useTitleData";
import { useRef, useState } from "react";
import BreadcrumbCustom from "~/custom/components/breadcrumbs/Breadcrumb";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import EntityIcon from "../../icons/EntityIcon";
import LWIcon from "../../icons/LWIcon";
import clsx from "clsx";
import { useTranslation } from "react-i18next";
import { useIsMobile } from "~/hooks/use-mobile";
import { useActionToggle } from "~/hooks/useActionToggle";
import ActionClosedIcon from "../../icons/ActionClosedIcon";
import ActionOpenIcon from "../../icons/ActionOpenIcon";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { useLoadingStore } from "~/hooks/useLoader"; import OnboardingSession from "~/modules/onboarding/components/OnboardingSession";

export default function ShadcnSidebarLayout({
  children,
  layout,
  menuItems,
}: {
  children: React.ReactNode;
  layout: "app" | "admin" | "docs";
  menuItems?: SideBarItem[];
}) {
  const params = useParams();
  const title = useTitleData() ?? "";
  const mainElement = useRef<HTMLElement>(null);
  const [onboardingModalOpen, setOnboardingModalOpen] = useState(false);
  const { t } = useTranslation();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const appOrAdminData = useAppOrAdminData();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  // const [isActionOpen, setIsActionOpen] = useState(true);

  const group = appOrAdminData.entityGroups.find((f) => f.slug === params.group);

  const menuItems2 = [
    {
      title: params?.group ? params.group.charAt(0).toUpperCase() + params.group.slice(1) : "",
      routePath: params?.tenant && params?.group ? `/app/${params.tenant}/g/${params.group}` : "#",
      icon: group?.icon ? (
        <EntityIcon className="h-4 w-4" icon={group.icon} />
      ) : (
        <svg stroke="currentColor" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
          />
        </svg>
      ),
    },
    {
      title: params?.entity ? params.entity.charAt(0).toUpperCase() + params.entity.slice(1) : "",
      routePath: params?.tenant && params?.entity ? 
        (params?.group ? `/app/${params.tenant}/g/${params.group}/${params.entity}` : `/app/${params.tenant}/${params.entity}`) 
        : "#",
    },
  ];

  // Step flags
  const pathParts = location.pathname.split("/");
  const entityIndex = pathParts.findIndex((part) => part === params?.entity);
  const hasStep = searchParams.has("step");
  const isEditStepFormWizard = searchParams.get("editStepFormWizard") === "true";
  const isNew = location.pathname.endsWith("/new") || (hasStep && !isEditStepFormWizard);
  const isEdit = location.pathname.endsWith("/edit") || isEditStepFormWizard;
  const path = location.pathname;

  const isLoading = useLoadingStore((state) => state.isLoading);

  // Determine entityId when NOT in add/step-only flow (but allow in editStepFormWizard)
  let entityId: string | null = null;
  if (!isNew && entityIndex !== -1 && pathParts.length > entityIndex + 1) {
    const nextPart = pathParts[entityIndex + 1];
    if (nextPart && nextPart !== "new") {
      entityId = nextPart;
    }
  }

  // Add "Details" breadcrumb
  if (entityId && params?.tenant && params?.group && params?.entity) {
    const detailLabel = `${params.entity.charAt(0).toUpperCase() + params.entity.slice(1)} Details`;
    const detailPath = `/app/${params.tenant}/g/${params.group}/${params.entity}/${entityId}`;

    const alreadyExists = menuItems2.some((item) => item.title === detailLabel && item.routePath === detailPath);

    if (!alreadyExists) {
      menuItems2.push({
        title: detailLabel,
        routePath: detailPath,
      });
    }
  }


  // Add breadcrumb for Add/Edit pages
  const capitalized = params?.entity && params.entity.length > 1 ? params.entity.charAt(0).toUpperCase() + params.entity.slice(1) : "";


  if (isNew) {
    menuItems2.push({
      title: `Add New ${capitalized}`,
      routePath: location.pathname + location.search,
    });
  } else if (isEdit) {
    menuItems2.push({
      title: `Edit ${capitalized}`,
      routePath: location.pathname + location.search,
    });
  }

  const isAppSide = layout === "app";
  const isMobile = useIsMobile();
  const { toggleActionsBar, isActionOpen } = useActionToggle();
  const isTablePage = !params?.id && location.pathname.includes("app") && !location.pathname.includes("edit");
  const editorCreatePage = location.pathname.includes("edit") || location.pathname.includes("new");
  const isOverPage = params?.id && !location.pathname.includes("edit") && location.pathname.includes("app");
  const isMainDashboardPage = location.pathname.includes("dashboard");
  const isAppProfilePage = location.pathname.startsWith("/app/") && location.pathname.includes("settings");
  const isAdminPage = location.pathname.includes("/admin");
  return (
    <SidebarProvider>
      <OnboardingSession open={onboardingModalOpen} setOpen={setOnboardingModalOpen} />
      <ShadcnAppSidebar layout={layout} items={menuItems} />
      <SidebarInset className={`${isOverPage || isTablePage ? "overflow-x-hidden overflow-y-auto" : ""} ${isAppProfilePage ? "max-h-screen" : ""}`}>
        {isMobile && (
          <div className="flex items-center justify-between">
            <div className="mx-3 flex h-[48px] items-center">
              <SidebarTrigger className="cursor-pointer pl-[11px]" />
              <span className="pl-[8px]">
                <LWIcon />
              </span>
              <span className="font-rotunda text-[14.33px] leading-[13.56px] font-medium tracking-normal">Linkworks</span>
            </div>

            {isOverPage && (
              <button
                className="cursor-pointer pr-4"
                onClick={() => {
                  toggleActionsBar();
                  console.log("clickeddd");
                }}
              >
                {!isActionOpen ? <ActionClosedIcon /> : <ActionOpenIcon />}
              </button>
            )}
          </div>
        )}
        {isAppSide ? (
          <header className="border-border flex shrink-0 items-center gap-2  pl-[12px] sticky top-[10px] z-10  w-fit">
            <div className={`flex items-center gap-1 truncate  ${!isOverPage ? "md:px-1" : ""}  ${isAppProfilePage ? "md:px-2" : ""}`}>
              {!isAppSide && (<>
                <SidebarTrigger className="-ml-1 cursor-pointer" />
                <Breadcrumb className="truncate">
                  <BreadcrumbList className="truncate">
                    <BreadcrumbItem className="block truncate">
                      <BreadcrumbPage className="truncate font-bold">{title}</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </>
              )}

              <div style={{
                boxShadow: "0px 4px 7.7px rgba(186, 186, 186, 0.25)",
              }} className={`flex  rounded-[8px] bg-white border border-input  shadow-xs  items-center w-full  ${isMainDashboardPage ? '!h-[28px] px-[10px]' : 'gap-3 h-[48px] px-[20px]'}`}>
                {menuItems2 && isAppSide && <> <div className="!px-0 "><SidebarTrigger className=" cursor-pointer" /></div>
                  {isAppSide && !isMainDashboardPage && <div className="  pb-1 !text-[#D5D5D5]">|</div>}
                  <div className="oveflow-y-hidden">
                    {isAppProfilePage ? (
                      <div className="text-[13px] font-semibold text-[#0B0A09]">{t("app.navbar.profileSettings")}</div>
                    ) : (
                      !isMainDashboardPage && <div className="ml-auto px-3 pr-[23px]">{/* Optional right actions */}</div>
                    )}
                    <BreadcrumbCustom menu={menuItems2} /></div></>}</div>
            </div>

          </header>) : (<header className="border-border flex h-12 shrink-0 items-center gap-1 border-b !bg-white">
            <div className="flex items-center gap-2 ml-1  truncate px-2">
              <SidebarTrigger className="-ml-1 cursor-pointer" />
              {!isAppSide && (
                <Breadcrumb className="truncate">
                  <BreadcrumbList className="truncate">
                    <BreadcrumbItem className="block truncate">
                      <BreadcrumbPage className="truncate font-bold">{title}</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              )}
              {isAppSide && !isMainDashboardPage && <div className="mx-3 h-full pb-1 !text-[#D5D5D5]">|</div>}
              <div>{menuItems2 && isAppSide && <BreadcrumbCustom className="" menu={menuItems2} />}</div>
            </div>
            {isAppProfilePage ? (
              <div className="text-[13px] font-semibold text-[#0B0A09]">{t("app.navbar.profileSettings")}</div>
            ) : (
              <div className="ml-auto px-3 pr-[23px]">{/* Optional right actions */}</div>
            )}
          </header>
        )}
        <main ref={mainElement} className={`relative flex-1 focus:outline-hidden`} tabIndex={0}>
          {isLoading && isAppSide && (
            <div className="absolute inset-0  left-0 z-[79]">
              <div className="absolute inset-0 -top-120 bg-transparent cursor-progress "></div>
            </div>

          )}
          <div key={params.tenant} className={` sm:pb-0 ${isTablePage || editorCreatePage || isAdminPage ? '' : ""}`}>
            {children}
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
